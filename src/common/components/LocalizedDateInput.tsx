import { useEffect } from 'react'
import { DateInput, useLocale } from 'react-admin'
import type { DateInputProps } from 'react-admin'

/**
 * A DateInput component that attempts to use the current locale from react-admin's i18nProvider.
 * Note: HTML5 date inputs are controlled by browser locale, but we can try to influence it.
 * This component sets the document locale to match react-admin's locale setting.
 */
export const LocalizedDateInput = (props: DateInputProps) => {
  const locale = useLocale()

  useEffect(() => {
    // Try to set the document locale to influence date input formatting
    if (locale && document.documentElement) {
      document.documentElement.lang = locale
    }
  }, [locale])

  return <DateInput {...props} />
}
